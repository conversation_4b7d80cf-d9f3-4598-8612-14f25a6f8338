/**
 * Shopify API Client
 *
 * This module provides a Shopify-specific API client with multistore support,
 * GraphQL and REST API capabilities, and proper error handling.
 */

import { ApiClient } from '../base/ApiClient.js';
import { ShopifyApiError, MultistoreError, AuthenticationError } from '../../errors/AppError.js';
import { getStoreConfig, isStoreSupported } from '../../config/index.js';

/**
 * Shopify API client with multistore support
 */
export class ShopifyClient extends ApiClient {
  constructor(config) {
    super('', {
      'Content-Type': 'application/json',
      'User-Agent': 'Americans United Inc App/1.0',
    });

    this.config = config;
    this.currentStore = null;
    this.currentSession = null;

    // Add REST API interface for compatibility
    this.rest = {
      get: this.restGet.bind(this),
      post: this.restPost.bind(this),
      put: this.restPut.bind(this),
      delete: this.restDelete.bind(this),
    };
  }

  /**
   * Set the current store context
   * @param {string} storeDomain - The store domain
   * @param {object} session - The session object with access token
   */
  setStoreContext(storeDomain, session = null) {
    if (!isStoreSupported(storeDomain)) {
      throw new MultistoreError(`Store '${storeDomain}' is not supported`);
    }

    this.currentStore = storeDomain;
    this.currentSession = session;

    // Update base URL for this store
    const apiVersion = this.config.shopify?.apiVersion || '2025-04';
    this.baseURL = `https://${storeDomain}/admin/api/${apiVersion}`;

    // Update the axios instance's baseURL
    this.client.defaults.baseURL = this.baseURL;

    console.log(`[ShopifyClient] Set store context for ${storeDomain}, baseURL: ${this.baseURL}`);
  }

  /**
   * Get store-specific API credentials
   * @param {string} storeDomain - The store domain (optional, uses current store if not provided)
   * @returns {object} - Store credentials
   */
  getStoreCredentials(storeDomain = null) {
    const store = storeDomain || this.currentStore;

    if (!store) {
      throw new MultistoreError('No store context set. Call setStoreContext() first.');
    }

    const storeConfig = getStoreConfig(store);
    if (!storeConfig) {
      throw new MultistoreError(`No configuration found for store '${store}'`);
    }

    return storeConfig;
  }

  /**
   * Get authorization headers for the current store
   * @returns {Promise<object>} - Authorization headers
   */
  async getAuthHeaders() {
    if (this.currentSession && this.currentSession.accessToken) {
      // Use session access token for authenticated requests
      return {
        'X-Shopify-Access-Token': this.currentSession.accessToken,
      };
    }

    // If no session is provided, try to retrieve access token from database
    if (this.currentStore) {
      try {
        const { getShop } = await import('../../../models/Shop.server.js');
        const shopData = await getShop(this.currentStore);

        if (shopData && shopData.accessToken) {
          return {
            'X-Shopify-Access-Token': shopData.accessToken,
          };
        }
      } catch (error) {
        console.error(`Failed to retrieve access token for ${this.currentStore}:`, error);
      }
    }

    // If we can't get an access token, throw an error
    throw new AuthenticationError(
      `No access token available for store '${this.currentStore}'. ` +
      'Either provide a session or ensure the store has been authenticated.'
    );
  }

  /**
   * Make authenticated request to Shopify API
   * @param {object} config - Request configuration
   * @returns {Promise<object>} - Response data
   */
  async authenticatedRequest(config) {
    if (!this.currentStore) {
      throw new MultistoreError('No store context set. Call setStoreContext() first.');
    }

    // Add authentication headers
    const authHeaders = await this.getAuthHeaders();
    const requestConfig = {
      ...config,
      headers: {
        ...this.defaultHeaders,
        ...authHeaders,
        ...config.headers,
      },
    };

    try {
      const response = await this.request(requestConfig);

      // Handle Shopify-specific errors
      if (response.status >= 400) {
        this.handleShopifyError(response);
      }

      return response;
    } catch (error) {
      if (error instanceof ShopifyApiError) {
        throw error;
      }

      throw new ShopifyApiError(
        error.message,
        error.response?.status || 500,
        {
          store: this.currentStore,
          url: config.url,
          method: config.method,
        }
      );
    }
  }

  /**
   * Handle Shopify-specific error responses
   * @param {object} response - Axios response object
   */
  handleShopifyError(response) {
    const { status, data } = response;
    let message = 'Shopify API error';

    if (data && data.errors) {
      if (typeof data.errors === 'string') {
        message = data.errors;
      } else if (Array.isArray(data.errors)) {
        message = data.errors.join(', ');
      } else if (typeof data.errors === 'object') {
        message = Object.values(data.errors).flat().join(', ');
      }
    }

    // Handle specific status codes
    switch (status) {
      case 401:
        throw new AuthenticationError(`Shopify authentication failed: ${message}`);
      case 402:
        throw new ShopifyApiError('Shopify shop is frozen or does not exist', status);
      case 403:
        throw new ShopifyApiError(`Shopify access forbidden: ${message}`, status);
      case 404:
        throw new ShopifyApiError('Shopify resource not found', status);
      case 422:
        throw new ShopifyApiError(`Shopify validation error: ${message}`, status);
      case 429:
        // Rate limiting is handled by the base ApiClient
        throw new ShopifyApiError('Shopify rate limit exceeded', status);
      default:
        throw new ShopifyApiError(message, status);
    }
  }

  /**
   * Get products from Shopify
   * @param {object} params - Query parameters
   * @returns {Promise<object>} - Products response
   */
  async getProducts(params = {}) {
    const response = await this.authenticatedRequest({
      method: 'GET',
      url: '/products.json',
      params,
    });

    return response.data;
  }

  /**
   * Get orders from Shopify
   * @param {object} params - Query parameters
   * @returns {Promise<object>} - Orders response
   */
  async getOrders(params = {}) {
    const response = await this.authenticatedRequest({
      method: 'GET',
      url: '/orders.json',
      params,
    });

    return response.data;
  }

  /**
   * Get a specific order by ID
   * @param {string|number} orderId - Order ID
   * @param {object} params - Query parameters
   * @returns {Promise<object>} - Order response
   */
  async getOrder(orderId, params = {}) {
    const response = await this.authenticatedRequest({
      method: 'GET',
      url: `/orders/${orderId}.json`,
      params,
    });

    return response.data;
  }

  /**
   * Update an order
   * @param {string|number} orderId - Order ID
   * @param {object} orderData - Order data to update
   * @returns {Promise<object>} - Updated order response
   */
  async updateOrder(orderId, orderData) {
    const response = await this.authenticatedRequest({
      method: 'PUT',
      url: `/orders/${orderId}.json`,
      data: { order: orderData },
    });

    return response.data;
  }

  /**
   * Get fulfillments for an order
   * @param {string|number} orderId - Order ID
   * @returns {Promise<object>} - Fulfillments response
   */
  async getFulfillments(orderId) {
    const response = await this.authenticatedRequest({
      method: 'GET',
      url: `/orders/${orderId}/fulfillments.json`,
    });

    return response.data;
  }

  /**
   * Get shop information
   * @returns {Promise<object>} - Shop response
   */
  async getShop() {
    const response = await this.authenticatedRequest({
      method: 'GET',
      url: '/shop.json',
    });

    return response.data;
  }

  /**
   * Execute GraphQL query
   * @param {string} query - GraphQL query string
   * @param {object} variables - Query variables
   * @returns {Promise<object>} - GraphQL response
   */
  async graphql(query, variables = {}) {
    const response = await this.authenticatedRequest({
      method: 'POST',
      url: '/graphql.json',
      data: {
        query,
        variables,
      },
    });

    const data = response.data;

    // Handle GraphQL errors
    if (data.errors && data.errors.length > 0) {
      const errorMessages = data.errors.map(error => error.message).join(', ');
      throw new ShopifyApiError(`GraphQL error: ${errorMessages}`, 400, {
        graphqlErrors: data.errors,
      });
    }

    return data;
  }

  /**
   * REST API GET method for compatibility
   * @param {object} options - Request options
   * @returns {Promise<object>} - Response object
   */
  async restGet(options) {
    const { path, session, query = {} } = options;

    // Set session context if provided
    if (session && session.shop) {
      this.setStoreContext(session.shop, session);
    }

    const response = await this.authenticatedRequest({
      method: 'GET',
      url: `/${path}.json`,
      params: query,
    });

    // Return response in expected format for fulfillment service utility
    return {
      status: response.status,
      json: async () => response.data,
    };
  }

  /**
   * REST API POST method for compatibility
   * @param {object} options - Request options
   * @returns {Promise<object>} - Response object
   */
  async restPost(options) {
    const { path, session, data } = options;

    if (session && session.shop) {
      this.setStoreContext(session.shop, session);
    }

    const response = await this.authenticatedRequest({
      method: 'POST',
      url: `/${path}.json`,
      data,
    });

    return {
      status: response.status,
      json: async () => response.data,
    };
  }

  /**
   * REST API PUT method for compatibility
   * @param {object} options - Request options
   * @returns {Promise<object>} - Response object
   */
  async restPut(options) {
    const { path, session, data } = options;

    if (session && session.shop) {
      this.setStoreContext(session.shop, session);
    }

    const response = await this.authenticatedRequest({
      method: 'PUT',
      url: `/${path}.json`,
      data,
    });

    return {
      status: response.status,
      json: async () => response.data,
    };
  }

  /**
   * REST API DELETE method for compatibility
   * @param {object} options - Request options
   * @returns {Promise<object>} - Response object
   */
  async restDelete(options) {
    const { path, session } = options;

    if (session && session.shop) {
      this.setStoreContext(session.shop, session);
    }

    const response = await this.authenticatedRequest({
      method: 'DELETE',
      url: `/${path}.json`,
    });

    return {
      status: response.status,
      json: async () => response.data,
    };
  }

  /**
   * Batch process multiple stores
   * @param {string[]} stores - Array of store domains
   * @param {Function} operation - Operation to perform on each store
   * @param {object} options - Batch processing options
   * @returns {Promise<object[]>} - Array of results
   */
  async batchProcess(stores, operation, options = {}) {
    const { concurrency = 3, continueOnError = true } = options;
    const results = [];
    const errors = [];

    // Process stores in batches
    for (let i = 0; i < stores.length; i += concurrency) {
      const batch = stores.slice(i, i + concurrency);

      const batchPromises = batch.map(async (store) => {
        try {
          this.setStoreContext(store);
          const result = await operation(store, this);
          return { store, success: true, data: result };
        } catch (error) {
          const errorResult = { store, success: false, error: error.message };

          if (continueOnError) {
            errors.push(errorResult);
            return errorResult;
          } else {
            throw error;
          }
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return {
      results,
      errors,
      totalProcessed: results.length,
      successCount: results.filter(r => r.success).length,
      errorCount: errors.length,
    };
  }
}
