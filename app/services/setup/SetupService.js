/**
 * Setup Service
 *
 * This service handles shop initialization, setup workflows,
 * and configuration management for new shops.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { TransactionManager } from '../../lib/database/TransactionManager.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';
import { normalizeShopDomain } from '../../utils/id-normalization.server.js';

/**
 * Service for shop setup operations
 */
export class SetupService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma;
    this.transactionManager = dependencies.transactionManager;
    this.pricingService = dependencies.pricingService;
    this.orderProcessingService = dependencies.orderProcessingService;
    this.initialized = false;

    if (!this.prisma || !this.pricingService || !this.orderProcessingService) {
      this.initializeAsync();
    } else {
      this.transactionManager = this.transactionManager || new TransactionManager(this.prisma);
      this.initialized = true;
    }
  }

  async initializeAsync() {
    if (!this.initialized) {
      this.prisma = this.prisma || await container.resolve('database');
      this.pricingService = this.pricingService || await container.resolve('pricingService');
      this.orderProcessingService = this.orderProcessingService || await container.resolve('orderProcessingService');
      this.transactionManager = this.transactionManager || new TransactionManager(this.prisma);
      this.initialized = true;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAsync();
    }
  }

  /**
   * Initialize a new shop with default configuration
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Setup options
   * @returns {Promise<object>} - Setup result
   */
  async initializeShop(shopDomain, options = {}) {
    await this.ensureInitialized();

    try {
      const {
        skipPricing = false,
        skipSetupFlag = false,
        skipBulkProcessing = false,
        defaultPricingData = null
      } = options;

      const normalizedShop = normalizeShopDomain(shopDomain);

      // Check if shop is already being set up
      const isInProgress = await this.checkAndMarkSetupInProgress(normalizedShop);
      if (isInProgress) {
        throw new BusinessLogicError('Shop setup is already in progress');
      }

      const setupResult = await this.transactionManager.executeTransaction(async (tx) => {
        const result = {
          shop: normalizedShop,
          setupFlag: null,
          pricing: null,
          bulkProcessing: null,
          errors: []
        };

        try {
          // Create setup flag if not skipped
          if (!skipSetupFlag) {
            result.setupFlag = await this.createSetupFlag(normalizedShop, tx);
          }

          // Initialize pricing if not skipped
          if (!skipPricing) {
            result.pricing = await this.initializeShopPricing(
              normalizedShop,
              defaultPricingData,
              tx
            );
          }

          return result;
        } catch (error) {
          result.errors.push(error.message);
          throw error;
        }
      });

      // Trigger bulk order processing if not skipped
      if (!skipBulkProcessing) {
        try {
          console.log(`[Setup Service] Starting bulk order processing for ${normalizedShop}`);
          setupResult.bulkProcessing = await this.initiateBulkOrderProcessing(normalizedShop);
          console.log(`[Setup Service] Bulk order processing completed for ${normalizedShop}:`, setupResult.bulkProcessing);
        } catch (error) {
          console.error(`[Setup Service] Bulk order processing failed for ${normalizedShop}:`, error);
          setupResult.errors.push(`Bulk processing failed: ${error.message}`);
          // Don't throw here - setup can continue without bulk processing
        }
      }

      // Reset processing flag after successful setup
      await this.resetProcessingFlag(normalizedShop);

      return setupResult;

    } catch (error) {
      // Ensure processing flag is reset on error
      await this.resetProcessingFlag(shopDomain);

      throw new BusinessLogicError(
        `Failed to initialize shop: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Check if setup is in progress and mark it as such
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<boolean>} - True if already in progress
   */
  async checkAndMarkSetupInProgress(shopDomain) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      const existingFlag = await this.prisma.setupFlag.findFirst({
        where: {
          shop: normalizedShop,
          isProcessing: true,
        },
      });

      if (existingFlag) {
        // Since we don't have updatedAt field, just return true if processing flag is set
        // In production, you might want to add a timestamp field to track processing start time
        return true;
      }

      // Mark as processing
      await this.prisma.setupFlag.upsert({
        where: {
          shop: normalizedShop,
        },
        update: {
          isProcessing: true,
        },
        create: {
          shop: normalizedShop,
          isProcessing: true,
          isSetup: false,
        },
      });

      return false;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to check setup progress: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Create setup flag for shop
   * @param {string} shopDomain - Shop domain
   * @param {object} tx - Transaction client
   * @returns {Promise<object>} - Created setup flag
   */
  async createSetupFlag(shopDomain, tx = null) {
    try {
      const prisma = tx || this.prisma;
      const normalizedShop = normalizeShopDomain(shopDomain);

      const setupFlag = await prisma.setupFlag.upsert({
        where: {
          shop: normalizedShop,
        },
        update: {
          isSetup: true,
          isProcessing: false,
        },
        create: {
          shop: normalizedShop,
          isSetup: true,
          isProcessing: false,
        },
      });

      return setupFlag;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to create setup flag: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Reset processing flag for shop
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<void>}
   */
  async resetProcessingFlag(shopDomain) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      await this.prisma.setupFlag.updateMany({
        where: {
          shop: normalizedShop,
        },
        data: {
          isProcessing: false,
        },
      });
    } catch (error) {
      console.error(`Failed to reset processing flag for ${shopDomain}:`, error);
      // Don't throw here as this is cleanup
    }
  }

  /**
   * Initialize shop pricing with default values
   * @param {string} shopDomain - Shop domain
   * @param {object} pricingData - Pricing data
   * @param {object} tx - Transaction client
   * @returns {Promise<object>} - Pricing initialization result
   */
  async initializeShopPricing(shopDomain, pricingData = null, tx = null) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      // Use default pricing data if none provided
      const defaultPricing = pricingData || await this.getDefaultPricingData();

      const results = {
        created: 0,
        updated: 0,
        errors: []
      };

      for (const [category, cost] of Object.entries(defaultPricing)) {
        try {
          await this.pricingService.setPrice({
            shop: normalizedShop,
            category: category,
            cost: cost
          });
          results.created++;
        } catch (error) {
          results.errors.push({
            category,
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to initialize shop pricing: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Get default pricing data
   * @returns {Promise<object>} - Default pricing data
   */
  async getDefaultPricingData() {
    try {
      // Import pricing data
      const { default: pricingData } = await import('../../pricingData/pricing.js');
      return pricingData;
    } catch (error) {
      console.error('Failed to load default pricing data:', error);
      return {};
    }
  }

  /**
   * Check if shop is set up
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<boolean>} - True if shop is set up
   */
  async isShopSetup(shopDomain) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      const setupFlag = await this.prisma.setupFlag.findFirst({
        where: {
          shop: normalizedShop,
        },
      });

      return setupFlag ? setupFlag.isSetup : false;
    } catch (error) {
      console.error(`Error checking shop setup status: ${error.message}`);
      return false;
    }
  }

  /**
   * Get shop setup status
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Setup status information
   */
  async getShopSetupStatus(shopDomain) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      const setupFlag = await this.prisma.setupFlag.findFirst({
        where: {
          shop: normalizedShop,
        },
      });

      if (!setupFlag) {
        return {
          isSetup: false,
          isProcessing: false,
          needsSetup: true
        };
      }

      return {
        isSetup: setupFlag.isSetup,
        isProcessing: setupFlag.isProcessing,
        needsSetup: !setupFlag.isSetup
      };
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to get shop setup status: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Process test orders for a shop
   * @param {string} shopDomain - Shop domain
   * @param {Array} testOrders - Array of test order data
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing results
   */
  async processTestOrders(shopDomain, testOrders, options = {}) {
    try {
      const {
        batchSize = 10,
        progressCallback = null
      } = options;

      const normalizedShop = normalizeShopDomain(shopDomain);
      const results = {
        processed: 0,
        successful: 0,
        failed: 0,
        errors: []
      };

      for (let i = 0; i < testOrders.length; i += batchSize) {
        const batch = testOrders.slice(i, i + batchSize);

        for (const orderData of batch) {
          try {
            if (progressCallback) {
              progressCallback(`Processing test order ${results.processed + 1}/${testOrders.length}`);
            }

            const result = await this.orderProcessingService.processOrder(
              orderData.id,
              normalizedShop,
              {
                skipValidation: true,
                dryRun: false,
                forceReprocess: true
              }
            );

            if (result.success) {
              results.successful++;
            } else {
              results.failed++;
              results.errors.push({
                orderId: orderData.id,
                error: 'Processing failed'
              });
            }

            results.processed++;
          } catch (error) {
            results.failed++;
            results.processed++;
            results.errors.push({
              orderId: orderData.id,
              error: error.message
            });
          }
        }
      }

      return results;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to process test orders: ${error.message}`,
        { shopDomain, orderCount: testOrders.length }
      );
    }
  }

  /**
   * Check if a bulk operation is running
   * @param {object} shopifyClient - Shopify client
   * @returns {Promise<object>} - Bulk operation status
   */
  async checkBulkRunning(shopifyClient) {
    try {
      const response = await shopifyClient.graphql(`
        query {
          currentBulkOperation {
            id
            status
            errorCode
            createdAt
            completedAt
            objectCount
            fileSize
            url
            type
          }
        }
      `);

      return response;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to check bulk operation status: ${error.message}`
      );
    }
  }

  /**
   * Start a bulk operation to get orders
   * @param {object} shopifyClient - Shopify client
   * @returns {Promise<object>} - Bulk operation response
   */
  async getBulkOrders(shopifyClient) {
    try {
      // Calculate Start of previous month. New downloads will be populated up to the beginning of the previous month
      const date = new Date();
      const previousMonthStart = new Date(date.getFullYear(), date.getMonth() - 1, 1).toISOString();
      console.log(`[getBulkOrders] Fetching orders since: ${previousMonthStart}`);

      const query = `
        {
          orders (query: "created_at:>=${previousMonthStart}") {
            edges {
              node {
                id
                createdAt
                lineItems {
                  edges {
                    node {
                      product {
                        productType
                        id
                      }
                      variant {
                        title
                        sku
                        id
                        inventoryItem {
                          id
                        }
                      }
                      quantity
                    }
                  }
                }
              }
            }
          }
        }
      `;

      console.log(`[getBulkOrders] Submitting bulk operation query`);
      console.log(`[getBulkOrders] Query being sent:`, JSON.stringify(query, null, 2));

      const response = await shopifyClient.graphql(`
        mutation bulkOperationRunQuery($query: String!) {
          bulkOperationRunQuery(query: $query) {
            bulkOperation {
              id
              status
            }
            userErrors {
              field
              message
            }
          }
        }
      `, {
        "query": query
      });

      // The response is already parsed JSON from ShopifyClient
      console.log(`[getBulkOrders] Bulk operation response:`, JSON.stringify(response, null, 2));

      if (response.data?.bulkOperationRunQuery?.userErrors?.length > 0) {
        const errors = response.data.bulkOperationRunQuery.userErrors;
        console.error(`[getBulkOrders] Bulk operation query returned errors:`, errors);
        throw new Error(`Bulk operation query failed: ${errors[0].message}`);
      }

      if (!response.data?.bulkOperationRunQuery?.bulkOperation) {
        console.error(`[getBulkOrders] Bulk operation query did not return a bulk operation:`, response);
        throw new Error('Bulk operation query did not return a bulk operation');
      }

      const bulkOperation = response.data.bulkOperationRunQuery.bulkOperation;
      console.log(`[getBulkOrders] Bulk operation query submitted successfully. ID: ${bulkOperation.id}, Status: ${bulkOperation.status}`);

      // Return the response
      return response;
    } catch (error) {
      console.error(`[getBulkOrders] Error submitting bulk operation query: ${error.message}`);
      throw error;
    }
  }

  /**
   * Initiate bulk order processing for a shop
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object>} - Processing results
   */
  async initiateBulkOrderProcessing(shopDomain) {
    try {
      const normalizedShop = normalizeShopDomain(shopDomain);

      // Get session with access token for this shop
      const { getShop } = await import('../../models/Shop.server.js');
      const shopData = await getShop(normalizedShop);

      if (!shopData || !shopData.accessToken) {
        throw new Error(`No valid session found for shop: ${normalizedShop}`);
      }

      // Get Shopify client and set store context with session
      const shopifyClient = await container.resolve('shopifyClient');
      const session = {
        shop: normalizedShop,
        accessToken: shopData.accessToken
      };
      shopifyClient.setStoreContext(normalizedShop, session);

      // Make shopifyClient compatible with fulfillment service utility
      // Add rest.get method that the utility expects
      if (!shopifyClient.rest) {
        shopifyClient.rest = {
          get: shopifyClient.restGet.bind(shopifyClient)
        };
      }

      // Check if there's already a bulk operation running
      const bulkStatus = await this.checkBulkRunning(shopifyClient);

      if (bulkStatus.data?.currentBulkOperation?.status === 'RUNNING') {
        console.log(`[Setup Service] Bulk operation already running for ${normalizedShop}, waiting for completion...`);
        // Wait for current operation to complete
        await this.waitForBulkCompletion(shopifyClient);
      }

      // Start new bulk operation
      console.log(`[Setup Service] Starting bulk order query for ${normalizedShop}`);
      const bulkResponse = await this.getBulkOrders(shopifyClient);

      if (bulkResponse.data?.bulkOperationRunQuery?.userErrors?.length > 0) {
        throw new Error(`Bulk operation failed: ${JSON.stringify(bulkResponse.data.bulkOperationRunQuery.userErrors)}`);
      }

      const bulkOperationId = bulkResponse.data?.bulkOperationRunQuery?.bulkOperation?.id;
      if (!bulkOperationId) {
        throw new Error('Failed to start bulk operation - no operation ID returned');
      }

      console.log(`[Setup Service] Bulk operation started with ID: ${bulkOperationId}`);

      // Wait for bulk operation to complete
      const completedOperation = await this.waitForBulkCompletion(shopifyClient);

      if (!completedOperation.url) {
        return {
          success: true,
          message: 'Bulk operation completed but no data URL available',
          processedCount: 0,
          skippedCount: 0,
          errorCount: 0
        };
      }

      // Process the bulk data
      console.log(`[Setup Service] Processing bulk data from URL for ${normalizedShop}`);
      const bulkSession = { shop: normalizedShop };
      const results = await this.populateFromBulk(completedOperation.url, bulkSession, shopifyClient, shopData);

      return {
        success: true,
        bulkOperationId,
        ...results
      };

    } catch (error) {
      console.error(`[Setup Service] Bulk order processing failed for ${shopDomain}:`, error);
      throw new BusinessLogicError(
        `Failed to initiate bulk order processing: ${error.message}`,
        { shopDomain }
      );
    }
  }

  /**
   * Wait for bulk operation to complete
   * @param {object} shopifyClient - Shopify client
   * @param {number} maxWaitTime - Maximum wait time in milliseconds
   * @returns {Promise<object>} - Completed bulk operation
   */
  async waitForBulkCompletion(shopifyClient, maxWaitTime = 300000) { // 5 minutes default
    const startTime = Date.now();
    const pollInterval = 5000; // 5 seconds

    while (Date.now() - startTime < maxWaitTime) {
      const bulkStatus = await this.checkBulkRunning(shopifyClient);
      const operation = bulkStatus.data?.currentBulkOperation;

      if (!operation) {
        throw new Error('No bulk operation found');
      }

      console.log(`[Setup Service] Bulk operation status: ${operation.status}`);

      if (operation.status === 'COMPLETED') {
        return operation;
      }

      if (operation.status === 'FAILED' || operation.status === 'CANCELED') {
        throw new Error(`Bulk operation ${operation.status.toLowerCase()}: ${operation.errorCode || 'Unknown error'}`);
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }

    throw new Error('Bulk operation timed out');
  }

  /**
   * Process bulk operation data with batch processing
   * @param {string} url - Bulk operation URL
   * @param {object} session - Shopify session
   * @param {object} shopifyClient - Shopify client
   * @returns {Promise<object>} - Processing results
   */
  async populateFromBulk(url, session, admin, shopData) {
    try {
      if (!url) {
        return {
          processedCount: 0,
          skippedCount: 0,
          errorCount: 0,
          unprocessableItems: [],
          skippedFulfillmentItems: [],
          noData: true
        };
      }

      // Fetch the bulk data
      const response = await fetch(url);
      const text = await response.text();

      if (!text.trim()) {
        return {
          processedCount: 0,
          skippedCount: 0,
          errorCount: 0,
          unprocessableItems: [],
          skippedFulfillmentItems: [],
          noData: true
        };
      }

      const lines = text.trim().split('\n');
      console.log(`[populateFromBulk] Starting batch processing of ${lines.length} lines for shop: ${session.shop}`);

      // Parse all lines first to prepare for batch processing
      const parsedItems = await this.parseAllLines(lines);
      console.log(`[populateFromBulk] Parsed ${parsedItems.length} valid items from ${lines.length} lines`);

      // Process items in batches
      const results = await this.processBulkItemsInBatches(parsedItems, session, admin, shopData);

      console.log(`[populateFromBulk] Completed batch processing for shop: ${session.shop}`);
      console.log(`[populateFromBulk] Final stats: processed ${results.processedCount} items, skipped ${results.skippedCount}, errors ${results.errorCount}`);

      // Log detailed information about skipped fulfillment items
      if (results.skippedFulfillmentItems && results.skippedFulfillmentItems.length > 0) {
        console.log(`[populateFromBulk] Skipped ${results.skippedFulfillmentItems.length} items due to fulfillment service:`);

        // Group by fulfillment service for better visibility
        const skippedByService = {};
        results.skippedFulfillmentItems.forEach(item => {
          const service = item.fulfillmentService || 'unknown';
          if (!skippedByService[service]) {
            skippedByService[service] = [];
          }
          skippedByService[service].push(item);
        });

        // Log summary by fulfillment service
        Object.entries(skippedByService).forEach(([service, items]) => {
          console.log(`  - ${service}: ${items.length} items`);
        });

        // Log detailed information for each skipped item
        console.log(`[populateFromBulk] Detailed skipped fulfillment items:`);
        results.skippedFulfillmentItems.forEach((item, index) => {
          console.log(`  ${index + 1}. SKU: ${item.sku}`);
          console.log(`     Product Type: ${item.productType}`);
          console.log(`     Title: ${item.title}`);
          console.log(`     Fulfillment Service: ${item.fulfillmentService || 'unknown'}`);
          console.log(`     Variant ID: ${item.variantId}`);
          console.log(`     Quantity: ${item.quantity}`);
          if (index < results.skippedFulfillmentItems.length - 1) {
            console.log(''); // Add blank line between items for readability
          }
        });
      }

      return results;
    } catch (error) {
      console.error(`[populateFromBulk] Critical error: ${error.message}`);
      console.error(`[populateFromBulk] Error stack:`, error.stack);
      console.error(`[populateFromBulk] URL: ${url}`);
      throw error;
    }
  }

  /**
   * Parse all lines from bulk data
   * @param {Array} lines - Raw lines from bulk data
   * @returns {Promise<Array>} - Array of parsed items
   */
  async parseAllLines(lines) {
    const parsedItems = [];
    let currentCreatedAt = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      try {
        const data = JSON.parse(line);

        // Track order creation date
        if (data.createdAt) {
          currentCreatedAt = data.createdAt;
          continue;
        }

        // Skip lines without product data
        if (!data.product && !data.variant) {
          continue;
        }

        // Ensure variant has required fields
        if (!data.variant || !data.variant.sku || !data.variant.title) {
          if (!data.variant) {
            data.variant = { sku: "", title: "" };
          } else if (!data.variant.sku) {
            data.variant.sku = "";
          } else {
            data.variant.title = "";
          }
        }

        // Add to parsed items with creation date
        parsedItems.push({
          ...data,
          createdAt: currentCreatedAt,
          lineIndex: i
        });

      } catch (parseError) {
        console.error(`[parseAllLines] Error parsing line ${i + 1}: ${parseError.message}`);
        // Continue parsing other lines
      }

      // Log progress for large datasets
      if ((i + 1) % 1000 === 0) {
        console.log(`[parseAllLines] Parsed ${i + 1}/${lines.length} lines (${Math.round((i + 1) / lines.length * 100)}%)`);
      }
    }

    return parsedItems;
  }

  /**
   * Process bulk items in batches with global fulfillment service optimization
   * @param {Array} items - Parsed items to process
   * @param {object} session - Shopify session
   * @param {object} admin - Admin client
   * @param {object} shopData - Shop data
   * @returns {Promise<object>} - Processing results
   */
  async processBulkItemsInBatches(items, session, admin, shopData) {
    // Use larger batch size since we're using proper batch processing with transactions
    const batchSize = 100; // Process 100 items at a time for better performance
    const results = {
      processedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      unprocessableItems: [],
      skippedFulfillmentItems: []
    };

    // MAJOR OPTIMIZATION: Pre-check ALL unique fulfillment services once
    console.log(`[processBulkItemsInBatches] Pre-checking fulfillment services for all ${items.length} items...`);
    const globalFulfillmentResults = await this.preCheckAllFulfillmentServices(items, session, admin, shopData);
    console.log(`[processBulkItemsInBatches] Fulfillment service pre-check completed`);

    // Split items into batches
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }

    console.log(`[processBulkItemsInBatches] Processing ${items.length} items in ${batches.length} batches of ${batchSize}`);
    console.log(`[processBulkItemsInBatches] Estimated processing time: ${Math.ceil(batches.length * 1.5)} seconds (with pre-checked fulfillment services)`);

    // Process each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      const startTime = Date.now();

      try {
        console.log(`[processBulkItemsInBatches] Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} items)`);

        // Pass the global fulfillment results to avoid re-checking
        const batchResults = await this.processBatchWithPreCheckedFulfillment(batch, session, admin, shopData, globalFulfillmentResults);

        // Accumulate results
        results.processedCount += batchResults.processedCount;
        results.skippedCount += batchResults.skippedCount;
        results.errorCount += batchResults.errorCount;
        results.unprocessableItems.push(...batchResults.unprocessableItems);
        results.skippedFulfillmentItems.push(...batchResults.skippedFulfillmentItems);

        const batchTime = Date.now() - startTime;
        const itemsPerSecond = Math.round(batch.length / (batchTime / 1000));

        // Log detailed progress
        const totalProcessed = Math.min((batchIndex + 1) * batchSize, items.length);
        const progressPercent = Math.round((totalProcessed / items.length) * 100);
        console.log(`[processBulkItemsInBatches] Batch ${batchIndex + 1} completed in ${batchTime}ms (${itemsPerSecond} items/sec)`);
        console.log(`[processBulkItemsInBatches] Progress: ${totalProcessed}/${items.length} (${progressPercent}%) - Processed: ${batchResults.processedCount}, Skipped: ${batchResults.skippedCount}, Errors: ${batchResults.errorCount}`);

        // Minimal delay between batches since fulfillment services are pre-checked
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 25));
        }

      } catch (batchError) {
        console.error(`[processBulkItemsInBatches] Error processing batch ${batchIndex + 1}: ${batchError.message}`);
        results.errorCount += batch.length;

        // Add all items in failed batch to unprocessable items
        batch.forEach(item => {
          results.unprocessableItems.push({
            error: `Batch processing failed: ${batchError.message}`,
            sku: item.variant?.sku,
            productType: item.product?.productType,
            title: item.variant?.title,
            quantity: item.quantity
          });
        });
      }
    }

    return results;
  }

  /**
   * Pre-check fulfillment services for ALL items at once - major optimization
   * @param {Array} items - All items to check
   * @param {object} session - Shopify session
   * @param {object} admin - Admin client
   * @param {object} shopData - Shop data
   * @returns {Promise<Map>} - Map of all variant IDs to fulfillment results
   */
  async preCheckAllFulfillmentServices(items, session, admin, shopData) {
    // Extract ALL unique variant IDs from the entire dataset
    const allUniqueVariantIds = [...new Set(items.map(item => item.variant?.id).filter(Boolean))];

    console.log(`[preCheckAllFulfillmentServices] Checking ${allUniqueVariantIds.length} unique variants across entire dataset`);

    // Import the fulfillment service utility
    const { batchCheckManualFulfillmentServices } = await import('../../utils/fulfillment-order-service.js');

    // Prepare admin client with shop context
    const adminWithShop = {
      ...admin,
      session: {
        ...admin.session,
        shop: session.shop,
        accessToken: shopData.accessToken
      },
      shop: session.shop
    };

    try {
      // Check ALL fulfillment services with ultra-conservative settings to avoid rate limits
      const batchResults = await batchCheckManualFulfillmentServices(
        adminWithShop,
        allUniqueVariantIds,
        null,
        {
          batchSize: 5, // Very small batch size
          concurrency: 1, // Sequential processing only
          delayBetweenBatches: 1000, // 1 second delay between batches
          onProgress: (progress) => {
            console.log(`[preCheckAllFulfillmentServices] Global progress: ${progress.processed}/${progress.total} variants (${progress.percentage}%)`);
          }
        }
      );

      // Convert to Map for faster lookups
      const fulfillmentMap = new Map();
      for (const [variantId, result] of Object.entries(batchResults)) {
        fulfillmentMap.set(variantId, result);
      }

      console.log(`[preCheckAllFulfillmentServices] Successfully pre-checked ${fulfillmentMap.size} variants`);
      return fulfillmentMap;

    } catch (error) {
      console.error(`[preCheckAllFulfillmentServices] Error: ${error.message}`);

      // Fallback: return empty map, individual batches will handle their own checks
      return new Map();
    }
  }

  /**
   * Process a batch with pre-checked fulfillment services - ultra-fast processing
   * @param {Array} batch - Batch of items to process
   * @param {object} session - Shopify session
   * @param {object} admin - Admin client
   * @param {object} shopData - Shop data
   * @param {Map} globalFulfillmentResults - Pre-checked fulfillment results
   * @returns {Promise<object>} - Batch processing results
   */
  async processBatchWithPreCheckedFulfillment(batch, session, admin, shopData, globalFulfillmentResults) {
    const batchResults = {
      processedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      unprocessableItems: [],
      skippedFulfillmentItems: []
    };

    // Filter items using pre-checked fulfillment results - NO API CALLS!
    const itemsToProcess = [];

    for (const item of batch) {
      const variantId = item.variant?.id;
      const fulfillmentResult = globalFulfillmentResults.get(variantId);

      // Skip items with non-manual fulfillment service
      if (fulfillmentResult && !fulfillmentResult.isManual) {
        batchResults.skippedFulfillmentItems.push({
          sku: item.variant.sku,
          productType: item.product.productType,
          title: item.variant.title,
          variantId: variantId,
          fulfillmentService: fulfillmentResult.serviceName,
          quantity: item.quantity,
          createdAt: item.createdAt
        });
        batchResults.skippedCount++;
      } else {
        itemsToProcess.push(item);
      }
    }

    // If no items to process, return early
    if (itemsToProcess.length === 0) {
      return batchResults;
    }

    // Convert items to order data format for batch processing
    const orderDataArray = itemsToProcess.map(item => {
      const variantId = item.variant?.id;
      const unitPrice = 0; // Let the processor handle actual pricing
      const lineTotal = unitPrice * item.quantity;

      const lineItem = {
        id: variantId,
        title: item.variant.title,
        quantity: item.quantity,
        variant_id: variantId?.split('/').pop(),
        sku: item.variant.sku,
        product_id: item.product?.id?.split('/').pop(),
        product_type: item.product?.productType,
        price: unitPrice.toString(),
        total_discount: '0.00',
        fulfillment_service: 'manual',
        requires_shipping: true,
        taxable: true,
        grams: 0,
        vendor: '',
        properties: [],
      };

      return {
        id: 'bulk_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        created_at: item.createdAt,
        line_items: [lineItem],
        subtotal_price: lineTotal.toString(),
        total_tax: '0.00',
        total_price: lineTotal.toString(),
      };
    });

    try {
      // Use the new batch processing method
      const batchProcessingResult = await this.orderProcessingService.processBatchWithData(
        orderDataArray,
        session.shop,
        {
          skipValidation: false,
          dryRun: false,
          forceReprocess: false
        }
      );

      // Process the results
      for (const result of batchProcessingResult.results) {
        if (result.success) {
          if (result.skipped) {
            batchResults.skippedCount++;
            // Find the corresponding item for detailed logging
            const correspondingItem = itemsToProcess.find(item =>
              result.orderId.includes(item.variant?.sku) ||
              result.reason === 'All line items skipped for this shop'
            );
            if (correspondingItem) {
              batchResults.skippedFulfillmentItems.push({
                sku: correspondingItem.variant.sku,
                productType: correspondingItem.product.productType,
                title: correspondingItem.variant.title,
                variantId: correspondingItem.variant.id,
                fulfillmentService: 'skipped_for_shop',
                quantity: correspondingItem.quantity,
                createdAt: correspondingItem.createdAt
              });
            }
          } else {
            batchResults.processedCount++;
          }
        } else {
          batchResults.errorCount++;
          // Find the corresponding item for error logging
          const correspondingItem = itemsToProcess.find(item =>
            result.orderId.includes(item.variant?.sku)
          );
          if (correspondingItem) {
            batchResults.unprocessableItems.push({
              error: result.error || 'Unknown error',
              sku: correspondingItem.variant.sku,
              productType: correspondingItem.product.productType,
              title: correspondingItem.variant.title,
              quantity: correspondingItem.quantity
            });
          }
        }
      }

    } catch (batchError) {
      console.error(`[processBatchWithPreCheckedFulfillment] Batch processing failed: ${batchError.message}`);
      // Mark all items as errors
      batchResults.errorCount += itemsToProcess.length;
      itemsToProcess.forEach(item => {
        batchResults.unprocessableItems.push({
          error: `Batch processing failed: ${batchError.message}`,
          sku: item.variant.sku,
          productType: item.product.productType,
          title: item.variant.title,
          quantity: item.quantity
        });
      });
    }

    return batchResults;
  }

  /**
   * Legacy method - kept for fallback compatibility
   * @param {Array} batch - Batch of items to process
   * @param {object} session - Shopify session
   * @param {object} admin - Admin client
   * @param {object} shopData - Shop data
   * @returns {Promise<object>} - Batch processing results
   */
  async processBatch(batch, session, admin, shopData) {
    const batchResults = {
      processedCount: 0,
      skippedCount: 0,
      errorCount: 0,
      unprocessableItems: [],
      skippedFulfillmentItems: []
    };

    // First, batch check fulfillment services for all items in this batch
    const fulfillmentChecks = await this.batchCheckFulfillmentServices(batch, session, admin, shopData);

    // Filter items that should be processed (have manual fulfillment service)
    const itemsToProcess = [];

    for (const item of batch) {
      const variantId = item.variant?.id;
      const fulfillmentResult = fulfillmentChecks.get(variantId);

      // Skip items with non-manual fulfillment service
      if (fulfillmentResult && !fulfillmentResult.isManual) {
        batchResults.skippedFulfillmentItems.push({
          sku: item.variant.sku,
          productType: item.product.productType,
          title: item.variant.title,
          variantId: variantId,
          fulfillmentService: fulfillmentResult.serviceName,
          quantity: item.quantity,
          createdAt: item.createdAt
        });
        batchResults.skippedCount++;
      } else {
        itemsToProcess.push(item);
      }
    }

    // If no items to process, return early
    if (itemsToProcess.length === 0) {
      return batchResults;
    }

    // Convert items to order data format for batch processing
    const orderDataArray = itemsToProcess.map(item => {
      const variantId = item.variant?.id;
      const unitPrice = 0; // Let the processor handle actual pricing
      const lineTotal = unitPrice * item.quantity;

      const lineItem = {
        id: variantId,
        title: item.variant.title,
        quantity: item.quantity,
        variant_id: variantId?.split('/').pop(),
        sku: item.variant.sku,
        product_id: item.product?.id?.split('/').pop(),
        product_type: item.product?.productType,
        price: unitPrice.toString(),
        total_discount: '0.00',
        fulfillment_service: 'manual',
        requires_shipping: true,
        taxable: true,
        grams: 0,
        vendor: '',
        properties: [],
      };

      return {
        id: 'bulk_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        created_at: item.createdAt,
        line_items: [lineItem],
        subtotal_price: lineTotal.toString(),
        total_tax: '0.00',
        total_price: lineTotal.toString(),
      };
    });

    try {
      // Use the new batch processing method
      const batchProcessingResult = await this.orderProcessingService.processBatchWithData(
        orderDataArray,
        session.shop,
        {
          skipValidation: false,
          dryRun: false,
          forceReprocess: false
        }
      );

      // Process the results
      for (const result of batchProcessingResult.results) {
        if (result.success) {
          if (result.skipped) {
            batchResults.skippedCount++;
            // Find the corresponding item for detailed logging
            const correspondingItem = itemsToProcess.find(item =>
              result.orderId.includes(item.variant?.sku) ||
              result.reason === 'All line items skipped for this shop'
            );
            if (correspondingItem) {
              batchResults.skippedFulfillmentItems.push({
                sku: correspondingItem.variant.sku,
                productType: correspondingItem.product.productType,
                title: correspondingItem.variant.title,
                variantId: correspondingItem.variant.id,
                fulfillmentService: 'skipped_for_shop',
                quantity: correspondingItem.quantity,
                createdAt: correspondingItem.createdAt
              });
            }
          } else {
            batchResults.processedCount++;
          }
        } else {
          batchResults.errorCount++;
          // Find the corresponding item for error logging
          const correspondingItem = itemsToProcess.find(item =>
            result.orderId.includes(item.variant?.sku)
          );
          if (correspondingItem) {
            batchResults.unprocessableItems.push({
              error: result.error || 'Unknown error',
              sku: correspondingItem.variant.sku,
              productType: correspondingItem.product.productType,
              title: correspondingItem.variant.title,
              quantity: correspondingItem.quantity
            });
          }
        }
      }

    } catch (batchError) {
      console.error(`[processBatch] Batch processing failed: ${batchError.message}`);
      // Mark all items as errors
      batchResults.errorCount += itemsToProcess.length;
      itemsToProcess.forEach(item => {
        batchResults.unprocessableItems.push({
          error: `Batch processing failed: ${batchError.message}`,
          sku: item.variant.sku,
          productType: item.product.productType,
          title: item.variant.title,
          quantity: item.quantity
        });
      });
    }

    return batchResults;
  }

  /**
   * Optimized batch check fulfillment services - eliminates redundant API calls
   * @param {Array} batch - Batch of items to check
   * @param {object} session - Shopify session
   * @param {object} admin - Admin client
   * @param {object} shopData - Shop data
   * @returns {Promise<Map>} - Map of variant ID to fulfillment result
   */
  async batchCheckFulfillmentServices(batch, session, admin, shopData) {
    const fulfillmentResults = new Map();

    // Import the fulfillment service utility
    const { batchCheckManualFulfillmentServices } = await import('../../utils/fulfillment-order-service.js');

    // Extract unique variant IDs to avoid duplicate API calls
    const uniqueVariantIds = new Set();
    const variantToItemsMap = new Map();

    batch.forEach(item => {
      const variantId = item.variant?.id;
      if (variantId) {
        uniqueVariantIds.add(variantId);
        if (!variantToItemsMap.has(variantId)) {
          variantToItemsMap.set(variantId, []);
        }
        variantToItemsMap.get(variantId).push(item);
      }
    });

    const uniqueVariantArray = Array.from(uniqueVariantIds);
    console.log(`[batchCheckFulfillmentServices] Checking ${uniqueVariantArray.length} unique variants (reduced from ${batch.length} items)`);

    if (uniqueVariantArray.length === 0) {
      return fulfillmentResults;
    }

    // Prepare admin client with shop context
    const adminWithShop = {
      ...admin,
      session: {
        ...admin.session,
        shop: session.shop,
        accessToken: shopData.accessToken
      },
      shop: session.shop
    };

    try {
      // Use the optimized batch checking function with aggressive settings
      const batchResults = await batchCheckManualFulfillmentServices(
        adminWithShop,
        uniqueVariantArray,
        null, // orderId not needed for fulfillment service check
        {
          batchSize: 20, // Smaller batches to respect rate limits
          concurrency: 5, // Moderate concurrency to avoid overwhelming API
          delayBetweenBatches: 500, // Reduced delay
          onProgress: (progress) => {
            console.log(`[batchCheckFulfillmentServices] Progress: ${progress.processed}/${progress.total} variants checked`);
          }
        }
      );

      // Map results back to all items (including duplicates)
      for (const [variantId, result] of Object.entries(batchResults)) {
        fulfillmentResults.set(variantId, result);
      }

      console.log(`[batchCheckFulfillmentServices] Completed checking ${uniqueVariantArray.length} unique variants`);

    } catch (error) {
      console.error(`[batchCheckFulfillmentServices] Batch checking failed: ${error.message}`);

      // Fallback: default all variants to manual fulfillment
      uniqueVariantArray.forEach(variantId => {
        fulfillmentResults.set(variantId, { isManual: true, serviceName: 'manual' });
      });
    }

    return fulfillmentResults;
  }


}
