/**
 * Order Processing Service
 *
 * This service orchestrates the complete order processing workflow,
 * including validation, calculation, and persistence of order data.
 */

import { container } from '../../lib/container/ServiceContainer.server.js';
import { TransactionManager } from '../../lib/database/TransactionManager.js';
import { ProcessorFactory } from './processors/ProcessorFactory.js';
import { FulfillmentValidator } from './validators/FulfillmentValidator.js';
import { PriceCalculator } from './calculators/PriceCalculator.js';
import { normalizeOrderId, normalizeShopDomain } from '../../utils/id-normalization.server.js';
import { BusinessLogicError, ValidationError } from '../../lib/errors/AppError.js';

/**
 * Main order processing orchestration service
 */
export class OrderProcessingService {
  constructor(dependencies = {}) {
    this.prisma = dependencies.prisma || container.resolve('database');
    this.shopifyClient = dependencies.shopifyClient || container.resolve('shopifyClient');
    this.transactionManager = dependencies.transactionManager || new TransactionManager(this.prisma);
    this.processorFactory = dependencies.processorFactory || new ProcessorFactory({
      prisma: this.prisma,
      pricingService: dependencies.pricingService,
      invoiceBalanceService: dependencies.invoiceBalanceService
    });
    this.fulfillmentValidator = dependencies.fulfillmentValidator || new FulfillmentValidator();
    this.priceCalculator = dependencies.priceCalculator || new PriceCalculator();
  }

  /**
   * Process a single order
   * @param {string|number} orderId - Order ID to process
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing result
   */
  async processOrder(orderId, shopDomain, options = {}) {
    const {
      forceReprocess = false,
      skipValidation = false,
      dryRun = false,
      session = null,
    } = options;

    try {
      // Normalize inputs
      const normalizedOrderId = normalizeOrderId(orderId);
      const normalizedShopDomain = normalizeShopDomain(shopDomain);

      // Check if order was already processed
      if (!forceReprocess) {
        const existingProcessing = await this.checkExistingProcessing(normalizedOrderId, normalizedShopDomain);
        if (existingProcessing) {
          return {
            success: true,
            skipped: true,
            reason: 'Order already processed',
            orderId: normalizedOrderId,
            existingResult: existingProcessing,
          };
        }
      }

      // Set shop context for API client
      this.shopifyClient.setStoreContext(normalizedShopDomain, session);

      // Fetch order data from Shopify
      const orderData = await this.fetchOrderData(normalizedOrderId);

      // Validate order for processing
      if (!skipValidation) {
        await this.validateOrderForProcessing(orderData, normalizedShopDomain);
      }

      // Process order within transaction
      const result = await this.transactionManager.executeTransaction(async (tx) => {
        return this.processOrderInternal(orderData, normalizedShopDomain, { dryRun, tx });
      });

      return {
        success: true,
        orderId: normalizedOrderId,
        shopDomain: normalizedShopDomain,
        result,
      };

    } catch (error) {
      console.error(`Order processing failed for order ${orderId}:`, error);

      // Don't log "all items skipped" as an unprocessable - this is normal business logic
      if (!error.message.includes('All line items skipped for this shop')) {
        // Log processing failure only for actual errors
        await this.logProcessingFailure(orderId, shopDomain, error);
      }

      // For "all items skipped", return a successful result instead of throwing
      if (error.message.includes('All line items skipped for this shop')) {
        console.log(`Order ${orderId} skipped - all line items excluded for shop ${shopDomain}`);
        return {
          success: true,
          orderId: normalizeOrderId(orderId),
          shopDomain: normalizeShopDomain(shopDomain),
          skipped: true,
          reason: 'All line items skipped for this shop',
          result: {
            orderId: normalizeOrderId(orderId),
            shopDomain: normalizeShopDomain(shopDomain),
            lineItems: [],
            totals: { subtotal: 0, tax: 0, total: 0, costOfGoods: 0, profit: 0 },
            metadata: { processedAt: new Date(), skipped: true }
          }
        };
      }

      throw new BusinessLogicError(
        `Failed to process order ${orderId}: ${error.message}`,
        { orderId, shopDomain, originalError: error.message }
      );
    }
  }

  /**
   * Process an order with existing order data (from bulk operations)
   * @param {object} orderData - Complete order data from Shopify
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing result
   */
  async processOrderWithData(orderData, shopDomain, options = {}) {
    const {
      forceReprocess = false,
      skipValidation = false,
      dryRun = false,
    } = options;

    try {
      // Normalize inputs
      const normalizedOrderId = normalizeOrderId(orderData.id);
      const normalizedShopDomain = normalizeShopDomain(shopDomain);

      // Check if order was already processed
      if (!forceReprocess) {
        const existingProcessing = await this.checkExistingProcessing(normalizedOrderId, normalizedShopDomain);
        if (existingProcessing) {
          return {
            success: true,
            skipped: true,
            reason: 'Order already processed',
            orderId: normalizedOrderId,
            existingResult: existingProcessing,
          };
        }
      }

      // Validate order for processing
      if (!skipValidation) {
        await this.validateOrderForProcessing(orderData, normalizedShopDomain);
      }

      // Process order within transaction
      const result = await this.transactionManager.executeTransaction(async (tx) => {
        return this.processOrderInternal(orderData, normalizedShopDomain, { dryRun, tx });
      });

      return {
        success: true,
        orderId: normalizedOrderId,
        shopDomain: normalizedShopDomain,
        result,
      };

    } catch (error) {
      console.error(`Order processing failed for order ${orderData.id}:`, error);

      // Don't log "all items skipped" as an unprocessable - this is normal business logic
      if (!error.message.includes('All line items skipped for this shop')) {
        // Log processing failure only for actual errors
        await this.logProcessingFailure(orderData.id, shopDomain, error);
      }

      // For "all items skipped", return a successful result instead of throwing
      if (error.message.includes('All line items skipped for this shop')) {
        console.log(`Order ${orderData.id} skipped - all line items excluded for shop ${shopDomain}`);
        return {
          success: true,
          orderId: normalizeOrderId(orderData.id),
          shopDomain: normalizeShopDomain(shopDomain),
          skipped: true,
          reason: 'All line items skipped for this shop',
          result: {
            orderId: normalizeOrderId(orderData.id),
            shopDomain: normalizeShopDomain(shopDomain),
            lineItems: [],
            totals: { subtotal: 0, tax: 0, total: 0, costOfGoods: 0, profit: 0 },
            metadata: { processedAt: new Date(), skipped: true }
          }
        };
      }

      throw new BusinessLogicError(
        `Failed to process order ${orderData.id}: ${error.message}`,
        { orderId: orderData.id, shopDomain, originalError: error.message }
      );
    }
  }

  /**
   * Process multiple orders in batch
   * @param {Array<object>} orders - Array of order objects with orderId and shopDomain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Batch processing result
   */
  async processBatch(orders, options = {}) {
    const {
      batchSize = 10,
      continueOnError = true,
      progressCallback = null,
    } = options;

    const results = [];
    const errors = [];

    for (let i = 0; i < orders.length; i += batchSize) {
      const batch = orders.slice(i, i + batchSize);

      const batchPromises = batch.map(async (orderInfo) => {
        try {
          const result = await this.processOrder(
            orderInfo.orderId,
            orderInfo.shopDomain,
            options
          );
          return result;
        } catch (error) {
          const errorResult = {
            success: false,
            orderId: orderInfo.orderId,
            shopDomain: orderInfo.shopDomain,
            error: error.message,
          };

          if (continueOnError) {
            errors.push(errorResult);
            return errorResult;
          } else {
            throw error;
          }
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Report progress
      if (progressCallback) {
        progressCallback({
          processed: results.length,
          total: orders.length,
          currentBatch: Math.floor(i / batchSize) + 1,
          totalBatches: Math.ceil(orders.length / batchSize),
        });
      }
    }

    return {
      totalProcessed: results.length,
      successful: results.filter(r => r.success).length,
      failed: errors.length,
      results,
      errors,
    };
  }

  /**
   * Process multiple orders with existing data in a single transaction
   * @param {Array} orderDataArray - Array of order data objects
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Batch processing results
   */
  async processBatchWithData(orderDataArray, shopDomain, options = {}) {
    const {
      skipValidation = false,
      dryRun = false,
    } = options;

    const normalizedShopDomain = normalizeShopDomain(shopDomain);
    const results = [];

    try {
      // Process all orders within a single transaction for better performance
      const batchResult = await this.transactionManager.executeTransaction(async (tx) => {
        const transactionResults = [];

        for (const orderData of orderDataArray) {
          try {
            const normalizedOrderId = normalizeOrderId(orderData.id);

            // Check for existing processing if not forcing reprocess
            if (!options.forceReprocess) {
              const existing = await this.checkExistingProcessing(normalizedOrderId, normalizedShopDomain);
              if (existing) {
                transactionResults.push({
                  success: true,
                  orderId: normalizedOrderId,
                  shopDomain: normalizedShopDomain,
                  skipped: true,
                  reason: 'Already processed'
                });
                continue;
              }
            }

            // Validate order for processing if not skipping validation
            if (!skipValidation) {
              await this.validateOrderForProcessing(orderData, normalizedShopDomain);
            }

            // Process order internal logic
            const result = await this.processOrderInternal(orderData, normalizedShopDomain, { dryRun, tx });

            transactionResults.push({
              success: true,
              orderId: normalizedOrderId,
              shopDomain: normalizedShopDomain,
              result,
            });

          } catch (error) {
            // Handle "all items skipped" as success
            if (error.message.includes('All line items skipped for this shop')) {
              transactionResults.push({
                success: true,
                orderId: normalizeOrderId(orderData.id),
                shopDomain: normalizedShopDomain,
                skipped: true,
                reason: 'All line items skipped for this shop',
                result: {
                  orderId: normalizeOrderId(orderData.id),
                  shopDomain: normalizedShopDomain,
                  lineItems: [],
                  totals: { subtotal: 0, tax: 0, total: 0, costOfGoods: 0, profit: 0 },
                  metadata: { processedAt: new Date(), skipped: true }
                }
              });
            } else {
              // Log actual errors
              await this.logProcessingFailure(orderData.id, shopDomain, error);

              transactionResults.push({
                success: false,
                orderId: normalizeOrderId(orderData.id),
                shopDomain: normalizedShopDomain,
                error: error.message
              });
            }
          }
        }

        return transactionResults;
      });

      return {
        results: batchResult,
        errors: batchResult.filter(r => !r.success),
        summary: {
          total: orderDataArray.length,
          successful: batchResult.filter(r => r.success).length,
          failed: batchResult.filter(r => !r.success).length,
          skipped: batchResult.filter(r => r.skipped).length,
        },
      };

    } catch (error) {
      console.error(`Batch processing failed for shop ${shopDomain}:`, error);
      throw new BusinessLogicError(
        `Failed to process batch of orders: ${error.message}`,
        { shopDomain, batchSize: orderDataArray.length }
      );
    }
  }

  /**
   * Fetch order data from Shopify using GraphQL
   * @param {string} orderId - Order ID
   * @returns {Promise<object>} - Order data
   */
  async fetchOrderData(orderId) {
    try {
      const query = `
        query getOrder($id: ID!) {
          order(id: $id) {
            id
            name
            createdAt
            financialStatus
            fulfillmentStatus
            lineItems(first: 250) {
              edges {
                node {
                  id
                  title
                  quantity
                  originalUnitPriceSet {
                    shopMoney {
                      amount
                      currencyCode
                    }
                  }
                  variant {
                    id
                    sku
                    fulfillmentService {
                      serviceName
                    }
                    product {
                      id
                      productType
                    }
                  }
                }
              }
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Order/${orderId}`
      };

      const response = await this.shopifyClient.graphql(query, variables);

      if (!response.data?.order) {
        throw new ValidationError(`Order ${orderId} not found`);
      }

      // Convert GraphQL response to format expected by processing logic
      const order = response.data.order;
      const orderData = {
        id: order.id.split('/').pop(),
        name: order.name,
        created_at: order.createdAt,
        line_items: order.lineItems?.edges?.map(edge => ({
          id: edge.node.id.split('/').pop(),
          title: edge.node.title,
          quantity: edge.node.quantity,
          variant_id: edge.node.variant?.id?.split('/').pop(),
          sku: edge.node.variant?.sku,
          product_id: edge.node.variant?.product?.id?.split('/').pop(),
          product_type: edge.node.variant?.product?.productType,
        })) || [],
      };

      return orderData;
    } catch (error) {
      throw new BusinessLogicError(
        `Failed to fetch order data: ${error.message}`,
        { orderId }
      );
    }
  }

  /**
   * Validate order for processing
   * @param {object} orderData - Order data from Shopify
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<void>}
   */
  async validateOrderForProcessing(orderData, shopDomain) {
    // Check if order has line items
    if (!orderData.line_items || orderData.line_items.length === 0) {
      throw new ValidationError('Order has no line items');
    }

    // Validate fulfillment requirements
    const fulfillmentValidation = await this.fulfillmentValidator.validateOrder(orderData, shopDomain);
    if (!fulfillmentValidation.isValid) {
      throw new ValidationError(
        `Order fulfillment validation failed: ${fulfillmentValidation.errors.join(', ')}`
      );
    }

    // Check if all items are skipped for this shop
    const allItemsSkipped = fulfillmentValidation.warnings.some(w => w.includes('All line items skipped for this shop'));
    if (allItemsSkipped) {
      throw new ValidationError('All line items skipped for this shop - no processing needed');
    }

    // Note: All orders should be processed regardless of financial status
  }

  /**
   * Internal order processing logic
   * @param {object} orderData - Order data
   * @param {string} shopDomain - Shop domain
   * @param {object} options - Processing options
   * @returns {Promise<object>} - Processing result
   */
  async processOrderInternal(orderData, shopDomain, options = {}) {
    const { dryRun = false, tx } = options;
    const prisma = tx || this.prisma;

    const processingResult = {
      orderId: orderData.id,
      shopDomain,
      lineItems: [],
      totals: {
        subtotal: 0,
        tax: 0,
        total: 0,
        costOfGoods: 0,
        profit: 0,
      },
      metadata: {
        processedAt: new Date(),
        dryRun,
      },
    };

    // Process each line item
    for (const lineItem of orderData.line_items) {
      const processor = this.processorFactory.getProcessor(lineItem);
      const processedItem = await processor.processLineItem(lineItem, orderData, shopDomain);

      processingResult.lineItems.push(processedItem);

      // Accumulate totals
      processingResult.totals.subtotal += processedItem.subtotal || 0;
      processingResult.totals.tax += processedItem.tax || 0;
      processingResult.totals.total += processedItem.total || 0;
      processingResult.totals.costOfGoods += processedItem.costOfGoods || 0;
    }

    // Calculate profit
    processingResult.totals.profit = processingResult.totals.total - processingResult.totals.costOfGoods;

    // Save processing result if not dry run
    if (!dryRun) {
      await this.saveProcessingResult(processingResult, prisma);
    }

    return processingResult;
  }

  /**
   * Check if order was already processed
   * @param {string} orderId - Order ID
   * @param {string} shopDomain - Shop domain
   * @returns {Promise<object|null>} - Existing processing result or null
   */
  async checkExistingProcessing(orderId, shopDomain) {
    try {
      const existing = await this.prisma.processedWebhook.findFirst({
        where: {
          orderId: orderId,
          shop: shopDomain,
          topic: 'order_processing',
        },
        orderBy: {
          processedAt: 'desc',
        },
      });

      return existing;
    } catch (error) {
      console.error('Error checking existing processing:', error);
      return null;
    }
  }

  /**
   * Save processing result to database
   * @param {object} result - Processing result
   * @param {object} prisma - Prisma client (transaction or regular)
   * @returns {Promise<void>}
   */
  async saveProcessingResult(result, prisma) {
    // Save to processed webhooks table
    await prisma.processedWebhook.create({
      data: {
        orderId: result.orderId,
        shop: result.shopDomain,
        topic: 'order_processing',
        webhookId: `bulk_${result.orderId}_${Date.now()}`,
        processedAt: result.metadata.processedAt,
      },
    });

    // Update invoice balance if applicable
    if (result.totals.total > 0) {
      await this.updateInvoiceBalance(result, prisma);
    }
  }

  /**
   * Update invoice balance for the shop
   * @param {object} result - Processing result
   * @param {object} prisma - Prisma client
   * @returns {Promise<void>}
   */
  async updateInvoiceBalance(result, prisma) {
    const processedAt = new Date(result.metadata.processedAt);
    const month = processedAt.getMonth() + 1; // JavaScript months are 0-based
    const year = processedAt.getFullYear();

    // Create invoice balance entry for this order
    await prisma.invoiceBalance.create({
      data: {
        shop: result.shopDomain,
        month: month,
        year: year,
        category: 'orders', // Default category
        quantity: 1,
        balance: result.totals.total,
      },
    });

    // Create transaction record
    const balanceRecord = await prisma.invoiceBalance.findFirst({
      where: {
        shop: result.shopDomain,
        month: month,
        year: year,
        category: 'orders',
      },
      orderBy: { id: 'desc' },
    });

    if (balanceRecord) {
      await prisma.invoiceTransaction.create({
        data: {
          invoiceBalanceId: balanceRecord.id,
          amount: result.totals.total,
          description: `Order ${result.orderId} processing`,
          metadata: JSON.stringify({
            orderId: result.orderId,
            costOfGoods: result.totals.costOfGoods,
            profit: result.totals.profit,
          }),
        },
      });
    }
  }

  /**
   * Log processing failure
   * @param {string} orderId - Order ID
   * @param {string} shopDomain - Shop domain
   * @param {Error} error - Error that occurred
   * @returns {Promise<void>}
   */
  async logProcessingFailure(orderId, shopDomain, error) {
    try {
      await this.prisma.unprocessable.create({
        data: {
          shop: shopDomain,
          errorField: 'orderId',
          message: error.message,
          variantId: String(orderId), // Store orderId in variantId field for now
        },
      });
    } catch (logError) {
      console.error('Failed to log processing failure:', logError);
    }
  }

  /**
   * Get processing statistics
   * @param {string} shopDomain - Shop domain (optional)
   * @param {object} dateRange - Date range filter
   * @returns {Promise<object>} - Processing statistics
   */
  async getProcessingStatistics(shopDomain = null, dateRange = {}) {
    const { startDate, endDate } = dateRange;

    const whereClause = {
      topic: 'order_processing',
      ...(shopDomain && { shop: shopDomain }),
      ...(startDate && endDate && {
        processedAt: {
          gte: startDate,
          lte: endDate,
        },
      }),
    };

    const [totalProcessed, totalFailed] = await Promise.all([
      this.prisma.processedWebhook.count({ where: whereClause }),
      this.prisma.unprocessable.count({
        where: {
          ...(shopDomain && { shop: shopDomain }),
          ...(startDate && endDate && {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          }),
        },
      }),
    ]);

    return {
      totalProcessed,
      totalFailed,
      successRate: totalProcessed + totalFailed > 0
        ? (totalProcessed / (totalProcessed + totalFailed)) * 100
        : 0,
    };
  }
}
